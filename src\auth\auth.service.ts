import { Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import * as bcrypt from 'bcrypt';
import { UsersService } from "src/users/users.service";
import { Users } from "src/users/schema/users.schema";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import { IAuth } from "./interfaces/auth.interfaces";
import { IUsers } from "src/users/interfaces/users.interface";

@Injectable()
export class AuthService {
    constructor(
        private readonly usersService: UsersService,
        private readonly jwtService: JwtService,
        @InjectModel(Users.name) private userModel: Model<IUsers>
    ) {}

    async signUp(signUpDto: CreateUserDto): Promise<IAuth> {
        const user: Omit<IUsers, "password"> = await this.usersService.createUser(signUpDto);
        const payload = { name: user.firstName, _id: user._id };
        const accessToken = await this.jwtService.signAsync(payload);
        return {
            ...user,
            accessToken
        };
    }
}